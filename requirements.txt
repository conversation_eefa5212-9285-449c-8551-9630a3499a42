aiohappyeyeballs==2.4.3 ; python_version >= "3.10" and python_version < "4.0"
aiohttp==3.10.10 ; python_version >= "3.10" and python_version < "4.0"
aiosignal==1.3.1 ; python_version >= "3.10" and python_version < "4.0"
annotated-types==0.7.0 ; python_version >= "3.10" and python_version < "4.0"
anyio==4.6.2.post1 ; python_version >= "3.10" and python_version < "4.0"
async-timeout==4.0.3 ; python_version >= "3.10" and python_version < "3.11"
attrs==24.2.0 ; python_version >= "3.10" and python_version < "4.0"
backoff==2.2.1 ; python_version >= "3.10" and python_version < "4.0"
beautifulsoup4==4.11.2 ; python_version >= "3.10" and python_version < "4.0"
black==24.10.0 ; python_version >= "3.10" and python_version < "4.0"
cachetools==5.5.0 ; python_version >= "3.10" and python_version < "4.0"
certifi==2024.8.30 ; python_version >= "3.10" and python_version < "4.0"
cffi==1.17.1 ; python_version >= "3.10" and python_version < "4.0"
charset-normalizer==3.4.0 ; python_version >= "3.10" and python_version < "4.0"
click==8.1.7 ; python_version >= "3.10" and python_version < "4.0"
colorama==0.4.6 ; python_version >= "3.10" and python_version < "4.0"
cryptography==41.0.4 ; python_version >= "3.10" and python_version < "4.0"
cssselect==1.2.0 ; python_version >= "3.10" and python_version < "4.0"
dataclasses-json==0.6.7 ; python_version >= "3.10" and python_version < "4.0"
distro==1.9.0 ; python_version >= "3.10" and python_version < "4.0"
exceptiongroup==1.2.2 ; python_version >= "3.10" and python_version < "3.11"
feedfinder2==0.0.4 ; python_version >= "3.10" and python_version < "4.0"
feedparser==6.0.11 ; python_version >= "3.10" and python_version < "4.0"
filelock==3.16.1 ; python_version >= "3.10" and python_version < "4.0"
frozenlist==1.5.0 ; python_version >= "3.10" and python_version < "4.0"
google-ai-generativelanguage==0.6.4 ; python_version >= "3.10" and python_version < "4.0"
google-api-core==2.22.0 ; python_version >= "3.10" and python_version < "4.0"
google-api-core[grpc]==2.22.0 ; python_version >= "3.10" and python_version < "4.0"
google-api-python-client==2.151.0 ; python_version >= "3.10" and python_version < "4.0"
google-auth-httplib2==0.2.0 ; python_version >= "3.10" and python_version < "4.0"
google-auth==2.35.0 ; python_version >= "3.10" and python_version < "4.0"
google-generativeai==0.5.4 ; python_version >= "3.10" and python_version < "4.0"
google==3.0.0 ; python_version >= "3.10" and python_version < "4.0"
googleapis-common-protos==1.65.0 ; python_version >= "3.10" and python_version < "4.0"
gpt4all==2.8.2 ; python_version >= "3.10" and python_version < "4.0"
greenlet==3.1.1 ; python_version < "3.13" and (platform_machine == "aarch64" or platform_machine == "ppc64le" or platform_machine == "x86_64" or platform_machine == "amd64" or platform_machine == "AMD64" or platform_machine == "win32" or platform_machine == "WIN32") and python_version >= "3.10"
grpcio-status==1.62.3 ; python_version >= "3.10" and python_version < "4.0"
grpcio==1.67.1 ; python_version >= "3.10" and python_version < "4.0"
h11==0.14.0 ; python_version >= "3.10" and python_version < "4.0"
httpcore==1.0.6 ; python_version >= "3.10" and python_version < "4.0"
httplib2==0.22.0 ; python_version >= "3.10" and python_version < "4.0"
httpx==0.25.2 ; python_version >= "3.10" and python_version < "4.0"
idna==3.10 ; python_version >= "3.10" and python_version < "4.0"
importlib-metadata==8.5.0 ; python_version >= "3.10" and python_version < "4.0"
iniconfig==2.0.0 ; python_version >= "3.10" and python_version < "4.0"
jeepney==0.8.0 ; python_version >= "3.10" and python_version < "4.0" and sys_platform == "linux"
jieba3k==0.35.1 ; python_version >= "3.10" and python_version < "4.0"
jiter==0.7.0 ; python_version >= "3.10" and python_version < "4.0"
joblib==1.4.2 ; python_version >= "3.10" and python_version < "4.0"
jsonpatch==1.33 ; python_version >= "3.10" and python_version < "4.0"
jsonpointer==3.0.0 ; python_version >= "3.10" and python_version < "4.0"
keyring==23.0.0 ; python_version >= "3.10" and python_version < "4.0"
langchain-community==0.0.38 ; python_version >= "3.10" and python_version < "4.0"
langchain-core==0.1.52 ; python_version >= "3.10" and python_version < "4.0"
langchain-text-splitters==0.0.2 ; python_version >= "3.10" and python_version < "4.0"
langchain==0.1.20 ; python_version >= "3.10" and python_version < "4.0"
langfuse==1.9.2 ; python_version >= "3.10" and python_version < "4.0"
langsmith==0.1.139 ; python_version >= "3.10" and python_version < "4.0"
loguru==0.7.2 ; python_version >= "3.10" and python_version < "4.0"
lxml==5.3.0 ; python_version >= "3.10" and python_version < "4.0"
markdown-it-py==3.0.0 ; python_version >= "3.10" and python_version < "4.0"
marshmallow==3.23.0 ; python_version >= "3.10" and python_version < "4.0"
mdurl==0.1.2 ; python_version >= "3.10" and python_version < "4.0"
monotonic==1.6 ; python_version >= "3.10" and python_version < "4.0"
multidict==6.1.0 ; python_version >= "3.10" and python_version < "4.0"
mypy-extensions==1.0.0 ; python_version >= "3.10" and python_version < "4.0"
newspaper3k==0.2.8 ; python_version >= "3.10" and python_version < "4.0"
nltk==3.9.1 ; python_version >= "3.10" and python_version < "4.0"
numpy==1.26.4 ; python_version >= "3.10" and python_version < "4.0"
openai==1.53.0 ; python_version >= "3.10" and python_version < "4.0"
orjson==3.10.10 ; python_version >= "3.10" and python_version < "4.0"
packaging==23.2 ; python_version >= "3.10" and python_version < "4.0"
pathspec==0.12.1 ; python_version >= "3.10" and python_version < "4.0"
pillow==11.0.0 ; python_version >= "3.10" and python_version < "4.0"
pinecone-client==3.2.2 ; python_version >= "3.10" and python_version < "4.0"
platformdirs==4.3.6 ; python_version >= "3.10" and python_version < "4.0"
pluggy==1.5.0 ; python_version >= "3.10" and python_version < "4.0"
prompt-toolkit==3.0.48 ; python_version >= "3.10" and python_version < "4.0"
propcache==0.2.0 ; python_version >= "3.10" and python_version < "4.0"
proto-plus==1.25.0 ; python_version >= "3.10" and python_version < "4.0"
protobuf==4.25.5 ; python_version >= "3.10" and python_version < "4.0"
pyasn1-modules==0.4.1 ; python_version >= "3.10" and python_version < "4.0"
pyasn1==0.6.1 ; python_version >= "3.10" and python_version < "4.0"
pycookiecheat==0.6.0 ; python_version >= "3.10" and python_version < "4.0"
pycparser==2.22 ; python_version >= "3.10" and python_version < "4.0"
pydantic-core==2.23.4 ; python_version >= "3.10" and python_version < "4.0"
pydantic==2.9.2 ; python_version >= "3.10" and python_version < "4.0"
pygments==2.18.0 ; python_version >= "3.10" and python_version < "4.0"
pyparsing==3.2.0 ; python_version >= "3.10" and python_version < "4.0"
pytest==8.3.3 ; python_version >= "3.10" and python_version < "4.0"
python-dateutil==2.9.0.post0 ; python_version >= "3.10" and python_version < "4.0"
pytz==2023.4 ; python_version >= "3.10" and python_version < "4.0"
pywin32-ctypes==0.2.3 ; python_version >= "3.10" and python_version < "4.0" and sys_platform == "win32"
pyyaml==6.0.2 ; python_version >= "3.10" and python_version < "4.0"
regex==2024.9.11 ; python_version >= "3.10" and python_version < "4.0"
requests-file==2.1.0 ; python_version >= "3.10" and python_version < "4.0"
requests-toolbelt==1.0.0 ; python_version >= "3.10" and python_version < "4.0"
requests==2.32.3 ; python_version >= "3.10" and python_version < "4.0"
rich==13.9.3 ; python_version >= "3.10" and python_version < "4.0"
rsa==4.9 ; python_version >= "3.10" and python_version < "4"
secretstorage==3.3.3 ; python_version >= "3.10" and python_version < "4.0" and sys_platform == "linux"
sgmllib3k==1.0.0 ; python_version >= "3.10" and python_version < "4.0"
six==1.16.0 ; python_version >= "3.10" and python_version < "4.0"
sniffio==1.3.1 ; python_version >= "3.10" and python_version < "4.0"
soupsieve==2.6 ; python_version >= "3.10" and python_version < "4.0"
sqlalchemy==2.0.36 ; python_version >= "3.10" and python_version < "4.0"
sqlmap==1.8.10 ; python_version >= "3.10" and python_version < "4.0"
tenacity==8.5.0 ; python_version >= "3.10" and python_version < "4.0"
tiktoken==0.6.0 ; python_version >= "3.10" and python_version < "4.0"
tinysegmenter==0.3 ; python_version >= "3.10" and python_version < "4.0"
tldextract==5.1.2 ; python_version >= "3.10" and python_version < "4.0"
toml==0.10.2 ; python_version >= "3.10" and python_version < "4.0"
tomli==2.0.2 ; python_version >= "3.10" and python_version < "3.11"
tqdm==4.66.6 ; python_version >= "3.10" and python_version < "4.0"
typing-extensions==4.12.2 ; python_version >= "3.10" and python_version < "4.0"
typing-inspect==0.9.0 ; python_version >= "3.10" and python_version < "4.0"
uritemplate==4.1.1 ; python_version >= "3.10" and python_version < "4.0"
urllib3==2.2.3 ; python_version >= "3.10" and python_version < "4.0"
wcwidth==0.2.13 ; python_version >= "3.10" and python_version < "4.0"
win32-setctime==1.1.0 ; python_version >= "3.10" and python_version < "4.0" and sys_platform == "win32"
wrapt==1.14.0 ; python_version >= "3.10" and python_version < "4.0"
yarl==1.17.1 ; python_version >= "3.10" and python_version < "4.0"
zipp==3.20.2 ; python_version >= "3.10" and python_version < "4.0"
