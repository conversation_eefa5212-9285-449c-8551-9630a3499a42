import dataclasses
import os


@dataclasses.dataclass
class GeminiConfig:
    model: str = "gemini-1.0-pro"
    api_base: str = None
    gemini_key = os.getenv("GOOGLE_API_KEY", None)
    log_dir: str = "logs"


@dataclasses.dataclass
class GPT4ALLConfig:
    model: str = "mistral-7b-instruct-v0.1.Q4_0.gguf"
    api_base: str = None
    log_dir = "logs"


@dataclasses.dataclass
class ChatGPTConfig:
    # model: str = "text-davinci-002-render-sha"
    model: str = "gpt-4-browsing"

    # api_base: str = "https://api.openai.com/v1"
    # set up the openai api base, default:"https://api.openai.com/v1"
    api_base: str = os.getenv("OPENAI_BASEURL", "https://api.openai.com/v1")

    log_dir: str = "logs"

    # set up the openai key
    openai_key = os.getenv("OPENAI_API_KEY", None)
    # set the user-agent below
    userAgent: str = (
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36"
    )
    # set cookie below
    cookie: str = os.getenv("CHATGPT_COOKIE", None)
    # curl command file
    curl_file: str = os.path.join(
        os.path.realpath(os.path.dirname(__file__)), "chatgpt_config_curl.txt"
    )

    if openai_key is None:
        print(
            'Your OPENAI key is not set. Please set it in the environment variable.\nIf you want to use chatGPT with no API, use "text-davinci-002-render-sha" in chat_config.py'
        )
    if cookie is None:
        print(
            "Your CHATGPT_COOKIE is not set. Please set it in the environment variable."
        )

    error_wait_time: float = 20
    is_debugging: bool = False
    proxies: dict = dataclasses.field(
        default_factory=lambda: {
            "http": "",
            "https": "",
        }
    )
    pinecone_api_key = os.getenv("PINECONE_API_KEY", None)
